import { browser } from '$app/environment';
import { getTracker, initializeTracking } from './core/tracker.svelte';
import type { TrackingConfig } from './types/tracking.types';

/**
 * Default tracking configuration with all required fields
 */
export const DEFAULT_TRACKING_CONFIG: TrackingConfig = {
    projectId: 'default-project',
    trackingUrl: 'http://localhost:8090',
    enablePageTracking: true,
    enableInteractionTracking: true,
    enableFormTracking: true,
    enableScrollTracking: true,
    enableNavigationTracking: true,
    enableClickTracking: true,
    enableAutoTracking: true,
    debugMode: false,
    batchSize: 10,
    flushInterval: 30000, // 30 seconds
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    pageThresholds: {
        '/': 60000,           // Home page - 1 minute
        '/login': 30000,      // Login page - 30 seconds
        '/register': 45000,   // Register page - 45 seconds
        '/dashboard': 120000, // Dashboard - 2 minutes
        '/profile': 90000,    // Profile page - 1.5 minutes
        '/settings': 60000,   // Settings page - 1 minute
        '/help': 180000,      // Help page - 3 minutes
        '/docs': 300000,      // Documentation - 5 minutes
        default: 45000        // Default - 45 seconds
    }
};

/**
 * Initialize tracking system with custom configuration
 * Main function to setup tracking in your app
 */
export function setupTracking(customConfig: Partial<TrackingConfig> = {}) {
    if (!browser) {
        if (import.meta.env.DEV) {
            console.warn('⚠️ Tracking can only be initialized in the browser');
        }
        return null;
    }

    const config = { ...DEFAULT_TRACKING_CONFIG, ...customConfig };

    try {
        const tracker = initializeTracking(config);
        if (import.meta.env.DEV) {
            console.log('🎯 Tracking system initialized successfully');
        }
        return tracker;
    } catch (error) {
        if (import.meta.env.DEV) {
            console.error('❌ Failed to initialize tracking system:', error);
        }
        return null;
    }
}

/**
 * Get the current tracker instance
 * Use this to access tracking methods in components
 */
export function getCurrentTracker() {
    if (!browser) return null;
    return getTracker();
}

/**
 * Tracking presets for common use cases
 */
export const TrackingPresets = {
    /**
     * Minimal tracking - only essential page views
     */
    minimal: (): Partial<TrackingConfig> => ({
        enablePageTracking: true,
        enableInteractionTracking: false,
        enableFormTracking: false,
        enableScrollTracking: false,
        enableNavigationTracking: true,
        enableClickTracking: false,
        batchSize: 5,
        flushInterval: 60000 // 1 minute
    }),

    /**
     * Standard tracking - balanced feature set (recommended)
     */
    standard: (): Partial<TrackingConfig> => ({
        enablePageTracking: true,
        enableInteractionTracking: true,
        enableFormTracking: true,
        enableScrollTracking: true,
        enableNavigationTracking: true,
        enableClickTracking: true,
        batchSize: 10,
        flushInterval: 30000 // 30 seconds
    }),

    /**
     * Development mode - frequent flushes for debugging
     */
    development: (): Partial<TrackingConfig> => ({
        enablePageTracking: true,
        enableInteractionTracking: true,
        enableFormTracking: true,
        enableScrollTracking: true,
        enableNavigationTracking: true,
        enableClickTracking: true,
        debugMode: true,
        batchSize: 1, // Immediate sending
        flushInterval: 5000, // 5 seconds
        sessionTimeout: 5 * 60 * 1000 // 5 minutes for testing
    }),

    /**
     * Privacy-focused - minimal data collection
     */
    privacy: (): Partial<TrackingConfig> => ({
        enablePageTracking: true,
        enableInteractionTracking: false,
        enableFormTracking: false,
        enableScrollTracking: false,
        enableNavigationTracking: true,
        enableClickTracking: false,
        batchSize: 5,
        flushInterval: 60000, // 1 minute
        sessionTimeout: 15 * 60 * 1000 // 15 minutes
    })
};

/**
 * Quick setup functions for different environments
 */

/**
 * Setup tracking for development environment
 */
export function setupTrackingForDev(projectId?: string, trackingUrl?: string) {
    return setupTracking({
        ...TrackingPresets.development(),
        projectId: projectId || 'dev-project',
        trackingUrl: trackingUrl || 'http://localhost:8090'
    });
}

/**
 * Setup tracking for production environment
 */
export function setupTrackingForProd(projectId: string, trackingUrl: string) {
    return setupTracking({
        ...TrackingPresets.standard(),
        projectId,
        trackingUrl
    });
}

/**
 * Setup privacy-friendly tracking
 */
export function setupPrivacyTracking(projectId: string, trackingUrl: string) {
    return setupTracking({
        ...TrackingPresets.privacy(),
        projectId,
        trackingUrl
    });
}

/**
 * Helper function to track custom events easily
 */
export function trackEvent(eventType: string, metadata: Record<string, any> = {}) {
    const tracker = getCurrentTracker();
    if (tracker) {
        return tracker.trackCustomEvent(eventType, metadata);
    }
    if (import.meta.env.DEV) {
        console.warn('⚠️ Tracker not initialized. Call setupTracking() first.');
    }
}

/**
 * Helper function to track page events
 */
export function trackPageView(pageName: string, metadata: Record<string, any> = {}) {
    return trackEvent('page_view', { pageName, ...metadata });
}

/**
 * Helper function to track button clicks
 */
export function trackButtonClick(buttonId: string, buttonText?: string, metadata: Record<string, any> = {}) {
    return trackEvent('button_click', {
        buttonId,
        buttonText: buttonText || buttonId,
        ...metadata
    });
}

/**
 * Helper function to track feature usage
 */
export function trackFeatureUsage(featureName: string, action: string, metadata: Record<string, any> = {}) {
    return trackEvent('feature_usage', {
        featureName,
        action,
        ...metadata
    });
}

/**
 * Export core functionality
 */
export { getTracker, initializeTracking } from './core/tracker.svelte';
export { TrackingEventFactory, TrackingUtils } from './utils/event-factory';
export { TrackingStorageClient } from './core/storage-client';
export { SessionManager } from './core/session-manager.svelte';

// 🚨 ERROR TRACKING EXPORTS - MODO PICA DAS GALAXIAS! 🚨
export {
    ErrorTracker,
    getErrorTracker,
    safeCall,
    withTimeout,
    logError,
    logComponentError,
    setupDiscordErrorLogging
} from './errors/error-tracker';

export {
    errorBoundary,
    loadComponent,
    TrackedFetch,
    trackedFetch,
    retryOperation,
    createFormErrorTracker,
    createStoreErrorTracker,
    createNavigationErrorTracker,
    setupGlobalErrorTracking
} from './errors/svelte-helpers';

// Export types for TypeScript users
export type {
    TrackingConfig,
    TrackingEvent,
    SimpleTrackingEvent,
    PageMetrics,
    SessionInfo,
    InteractionEvent,
    FormEvent,
    EventType,
    EventMetadata,
    // Error tracking types 🔥
    ErrorConfig,
    ErrorEvent,
    LoadingTimeoutOptions
} from './types/tracking.types';