// Configurações de thresholds para diferentes páginas

import type { PageThreshold } from '../types/tracking.types';

export const DEFAULT_PAGE_THRESHOLDS_ARRAY: PageThreshold[] = [
    {
        path: '/',
        expectedTime: 60000, // 1 minuto
        description: 'Home page - se ficar mais que 1min, usuário está interessado'
    },
    {
        path: '/auth/login',
        expectedTime: 30000, // 30 segundos
        description: 'Login - se ficar mais que 30s, pode estar com dificuldades'
    },
    {
        path: '/auth/register',
        expectedTime: 120000, // 2 minutos
        description: 'Registro - tempo normal para preencher formulário'
    },
    {
        path: '/dashboard',
        expectedTime: 180000, // 3 minutos
        description: 'Dashboard - tempo de uso/exploração normal'
    },
    {
        path: '/profile',
        expectedTime: 90000, // 1.5 minutos
        description: 'Perfil - tempo para visualizar/editar dados'
    }
];

// Convert array to object format for easy lookup
export const DEFAULT_PAGE_THRESHOLDS: Record<string, number> & { default: number } = {
    '/': 60000,
    '/auth/login': 30000,
    '/auth/register': 120000,
    '/dashboard': 180000,
    '/profile': 90000,
    default: 60000
};

export const SCROLL_MILESTONES = [25, 50, 75, 90, 100]; // Porcentagens para tracking de scroll

export const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutos de inatividade

export const BATCH_SIZE = 10; // Número de eventos para enviar por batch

export const BATCH_INTERVAL = 30000; // 30 segundos para enviar batch