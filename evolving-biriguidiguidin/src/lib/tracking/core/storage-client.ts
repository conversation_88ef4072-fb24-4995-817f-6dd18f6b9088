// Cliente PocketBase para o serviço de tracking

import PocketBase from 'pocketbase';
import type { TrackingEvent } from '../types/tracking.types';

class TrackingStorageClient {
    private pb: PocketBase;
    private eventQueue: TrackingEvent[] = [];
    private isOnline = true;

    constructor(trackingUrl: string) {
        this.pb = new PocketBase(trackingUrl);
        this.setupConnectionMonitoring();
    }

    private setupConnectionMonitoring() {
        if (typeof window !== 'undefined') {
            window.addEventListener('online', () => {
                this.isOnline = true;
                this.flushQueue();
            });

            window.addEventListener('offline', () => {
                this.isOnline = false;
            });
        }
    }

    async saveEvent(event: TrackingEvent): Promise<void> {
        try {
            if (this.isOnline) {
                await this.pb.collection('tracking_events').create(event);
            } else {
                // Adiciona à queue se offline
                this.queueEvent(event);
            }
        } catch (error) {
            if (import.meta.env.DEV) {
                console.warn('Failed to save tracking event:', error);
            }
            // Adiciona à queue em caso de erro
            this.queueEvent(event);
        }
    }

    async saveBatch(events: TrackingEvent[]): Promise<void> {
        if (!this.isOnline || events.length === 0) {
            events.forEach(event => this.queueEvent(event));
            return;
        }

        try {
            // PocketBase não tem batch create nativo, então fazemos Promise.all
            const promises = events.map(event =>
                this.pb.collection('tracking_events').create(event)
            );

            await Promise.allSettled(promises);
        } catch (error) {
            if (import.meta.env.DEV) {
                console.warn('Failed to save tracking batch:', error);
            }
            events.forEach(event => this.queueEvent(event));
        }
    }

    private queueEvent(event: TrackingEvent) {
        this.eventQueue.push(event);

        // Limita o tamanho da queue (últimos 100 eventos)
        if (this.eventQueue.length > 100) {
            this.eventQueue = this.eventQueue.slice(-100);
        }

        // Salva no localStorage como backup
        this.saveQueueToStorage();
    }

    private async flushQueue() {
        if (this.eventQueue.length === 0) return;

        const eventsToFlush = [...this.eventQueue];
        this.eventQueue = [];

        try {
            await this.saveBatch(eventsToFlush);
            this.clearStorageQueue();
        } catch (error) {
            // Se falhar, recoloca na queue
            this.eventQueue = [...eventsToFlush, ...this.eventQueue];
        }
    }

    private saveQueueToStorage() {
        if (typeof window !== 'undefined') {
            try {
                localStorage.setItem('tracking_queue', JSON.stringify(this.eventQueue));
            } catch (error) {
                if (import.meta.env.DEV) {
                    console.warn('Failed to save tracking queue to storage:', error);
                }
            }
        }
    }

    private loadQueueFromStorage() {
        if (typeof window !== 'undefined') {
            try {
                const stored = localStorage.getItem('tracking_queue');
                if (stored) {
                    this.eventQueue = JSON.parse(stored);
                }
            } catch (error) {
                if (import.meta.env.DEV) {
                    console.warn('Failed to load tracking queue from storage:', error);
                }
            }
        }
    }

    private clearStorageQueue() {
        if (typeof window !== 'undefined') {
            localStorage.removeItem('tracking_queue');
        }
    }

    // Recupera queue do localStorage na inicialização
    init() {
        this.loadQueueFromStorage();
        if (this.isOnline && this.eventQueue.length > 0) {
            this.flushQueue();
        }
    }

    // Força flush da queue (útil para quando usuário sai da página)
    async forceFlush(): Promise<void> {
        await this.flushQueue();
    }

    // Método para testar conexão
    async testConnection(): Promise<boolean> {
        try {
            await this.pb.health.check();
            return true;
        } catch {
            return false;
        }
    }
}

export { TrackingStorageClient };