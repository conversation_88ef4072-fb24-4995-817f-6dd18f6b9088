# 🚨 ERROR TRACKING MODULE - MODO PICA DAS GALAXIAS! 🚀

O sistema de error tracking mais avançado do universo! Captura 3 tipos de erros com **rastreamento automático de origem** e integração zero-config com Discord.

## 🔥 NOVIDADES v2.0 - RASTREAMENTO DE ORIGEM!

### 📍 **Rastreamento Automático de Origem**
Agora o sistema mostra **exatamente** onde cada erro foi chamado:
- **Função:** Nome da função que chamou o error tracker
- **Arquivo:** Nome do arquivo onde a função está
- **Linha:** Número da linha no código (quando possível)  
- **Stack completo:** Stack trace completo para análise

### 🎯 **Logs Inteligentes por Ambiente**
- **Desenvolvimento:** Logs detalhados no console + Discord (se configurado)
- **Produção:** Apenas Discord (logs silenciosos no console)
- **Automático:** Detecta ambiente via `import.meta.env.DEV`

## 🎨 Como aparece no Console (DEV):

```
🧡 ERROR CAUGHT - HIGH
TypeError: Cannot read property 'price' of undefined
📍 ORIGEM: calculateTotal em +page.svelte:42
📄 Página: /checkout
🏷️ Tags: ['type:caught', 'environment:development', 'error:typeerror']
📚 Stack completo: TypeError: Cannot...
```

## 🎮 Como aparece no Discord:

```
🧡 TypeError - CAUGHT
```
Cannot read property 'price' of undefined
```

📍 Página: /checkout
🎚️ Severidade: HIGH  
🌍 Ambiente: development

🏷️ Tags: type:caught, environment:development, error:typeerror
📚 Stack Trace: ```
TypeError: Cannot read property...
```

## 🎯 Features Épicas

- ✅ **Discord Webhook Integration** - Logs automáticos em tempo real
- ✅ **3 Tipos de Erros** - Try/catch, componentes e timeout
- ✅ **Zero Configuração** - Funciona out-of-the-box
- ✅ **Retry Logic** - Tentativas automáticas em operações
- ✅ **Severity Levels** - Low, Medium, High, Critical
- ✅ **Smart Tagging** - Tags automáticas inteligentes
- ✅ **Svelte Integration** - Actions, stores, forms
- ✅ **TypeScript First** - 100% tipado

## 🚀 Setup Rápido

### 1. Setup Global (no seu +layout.svelte)

```typescript
<script>
  import { onMount } from 'svelte';
  import { setupGlobalErrorTracking } from '$lib/tracking/errors/svelte-helpers';

  onMount(() => {
    // Setup básico (desenvolvimento)
    setupGlobalErrorTracking(
      undefined, // Sem Discord por enquanto
      'Meu App Fantástico',
      'development'
    );

    // OU Setup completo com Discord (produção)
    setupGlobalErrorTracking(
      'https://discord.com/api/webhooks/YOUR_WEBHOOK_URL',
      'Meu App Fantástico',
      'production'
    );
  });
</script>
```

### 2. Discord Webhook Setup

1. Vá no seu servidor Discord
2. Server Settings → Integrations → Webhooks
3. Create Webhook → Copy Webhook URL
4. Use a URL no setup acima

## 🕸️ Uso - Try/Catch Errors

### Método 1: Wrapper Automático (RECOMENDADO)

```typescript
<script>
  import { safeCall, withTimeout } from '$lib/tracking/errors/svelte-helpers';

  async function fetchUserData() {
    // Automaticamente loga erros se falhar
    const user = await safeCall(
      () => fetch('/api/user').then(r => r.json()),
      'fetch_user_data'
    );

    if (user) {
      console.log('Usuário carregado:', user);
    } else {
      console.log('Falha ao carregar usuário (erro já logado)');
    }
  }

  async function fetchWithTimeout() {
    // Com timeout de 5 segundos
    const data = await withTimeout(
      () => fetch('/api/slow-endpoint').then(r => r.json()),
      5000,
      'API demorou muito para responder'
    );

    return data; // null se falhar ou timeout
  }
</script>
```

### Método 2: HTTP Client Integrado

```typescript
<script>
  import { trackedFetch } from '$lib/tracking/errors/svelte-helpers';

  async function apiCall() {
    // GET com tracking automático
    const users = await trackedFetch.get('/api/users');
    
    // POST com tracking automático
    const newUser = await trackedFetch.post('/api/users', {
      name: 'João',
      email: '<EMAIL>'
    });

    // PUT/DELETE também disponíveis
    const updated = await trackedFetch.put(`/api/users/${id}`, userData);
    await trackedFetch.delete(`/api/users/${id}`);
  }
</script>
```

### Método 3: Log Manual

```typescript
<script>
  import { logError } from '$lib/tracking/errors/svelte-helpers';

  async function manualErrorHandling() {
    try {
      await someOperation();
    } catch (error) {
      // Log manual com contexto adicional
      logError(error, 'some_operation', {
        userId: currentUser.id,
        timestamp: Date.now(),
        customData: 'qualquer coisa'
      });
      
      // Sua lógica de fallback aqui
      showErrorMessage('Ops, algo deu errado!');
    }
  }
</script>
```

## 🎨 Uso - Component Errors

### Error Boundary Action

```svelte
<script>
  import { errorBoundary } from '$lib/tracking/errors/svelte-helpers';
</script>

<!-- Captura erros neste componente e filhos -->
<div use:errorBoundary={{
  componentName: 'UserProfile',
  onError: (error) => {
    console.log('Erro capturado:', error);
    showFallbackUI = true;
  }
}}>
  <UserAvatar {user} />
  <UserInfo {user} />
  <UserActions {user} />
</div>
```

### Dynamic Component Loading

```typescript
<script>
  import { loadComponent } from '$lib/tracking/errors/svelte-helpers';

  let MyComponent;

  onMount(async () => {
    // Carrega componente com timeout e tracking
    MyComponent = await loadComponent(
      () => import('./HeavyComponent.svelte'),
      'HeavyComponent',
      5000 // 5 segundos timeout
    );

    if (!MyComponent) {
      console.log('Componente falhou ao carregar');
      // Fallback logic
    }
  });
</script>

{#if MyComponent}
  <svelte:component this={MyComponent} />
{:else}
  <div>Carregando componente...</div>
{/if}
```

### Manual Component Error

```typescript
<script>
  import { logComponentError } from '$lib/tracking/errors/svelte-helpers';

  function handleComponentError(error) {
    logComponentError(error, 'UserDashboard', {
      props: { userId, theme },
      state: 'loading'
    });
  }
</script>
```

## ⏱️ Uso - Loading Timeout Errors

### Método 1: Wrapper com Timeout

```typescript
<script>
  import { getErrorTracker } from '$lib/tracking/errors/svelte-helpers';

  const errorTracker = getErrorTracker();

  async function heavyOperation() {
    const result = await errorTracker.withTimeout(
      () => processLargeDataset(hugeData),
      {
        timeout: 10000, // 10 segundos
        errorMessage: 'Processamento demorou muito tempo',
        context: { 
          dataSize: hugeData.length,
          operation: 'data_processing'
        },
        retryable: true
      }
    );

    return result; // null se timeout
  }
</script>
```

### Método 2: Timeout Global

```typescript
<script>
  import { withTimeout } from '$lib/tracking/errors/svelte-helpers';

  async function apiCallWithTimeout() {
    const data = await withTimeout(
      () => fetch('/api/heavy-computation').then(r => r.json()),
      8000, // 8 segundos
      'API de computação pesada demorou muito'
    );

    return data;
  }
</script>
```

## 🎮 Uso Avançado

### Form Error Tracking

```svelte
<script>
  import { createFormErrorTracker } from '$lib/tracking/errors/svelte-helpers';

  const formTracker = createFormErrorTracker('UserRegistration');

  async function handleSubmit() {
    const result = await formTracker.safeSubmit(
      () => registerUser(formData),
      formData
    );

    if (result) {
      goto('/success');
    } else {
      // Erro já foi logado
      showErrorMessage('Falha no registro');
    }
  }

  function validateField(field, value) {
    if (!value) {
      formTracker.logValidationError(field, 'Campo obrigatório', value);
      return false;
    }
    return true;
  }
</script>
```

### Store Error Tracking

```typescript
// user.store.ts
import { writable } from 'svelte/store';
import { createStoreErrorTracker } from '$lib/tracking/errors/svelte-helpers';

const storeTracker = createStoreErrorTracker('UserStore');

export const userStore = writable(null);

export async function loadUser(id: string) {
  const user = await storeTracker.safeUpdate(
    () => fetchUser(id),
    'load_user'
  );

  if (user) {
    userStore.set(user);
  }
  
  return user;
}
```

### Retry Operations

```typescript
<script>
  import { retryOperation } from '$lib/tracking/errors/svelte-helpers';

  async function unreliableOperation() {
    // Tenta 3 vezes com delay crescente
    const result = await retryOperation(
      () => fetch('/api/unstable-endpoint').then(r => r.json()),
      3, // máximo 3 tentativas
      1000, // delay base de 1 segundo
      'unstable_api_call'
    );

    return result;
  }
</script>
```

## 🔧 Configuração Avançada

### Custom Error Config

```typescript
<script>
  import { getErrorTracker } from '$lib/tracking/errors/svelte-helpers';

  const customTracker = getErrorTracker({
    discordWebhookUrl: 'YOUR_WEBHOOK_URL',
    enableDiscordLogging: true,
    enableConsoleLogging: true,
    loadingTimeoutThreshold: 15000, // 15 segundos
    retryAttempts: 5,
    environment: 'production',
    projectName: 'Meu App Épico'
  });
</script>
```

### Environment-Based Setup

```typescript
<script>
  import { setupGlobalErrorTracking } from '$lib/tracking/errors/svelte-helpers';
  import { dev } from '$app/environment';

  onMount(() => {
    if (dev) {
      // Desenvolvimento - só console
      setupGlobalErrorTracking(
        undefined,
        'Meu App',
        'development'
      );
    } else {
      // Produção - Discord + console
      setupGlobalErrorTracking(
        'https://discord.com/api/webhooks/PROD_WEBHOOK',
        'Meu App',
        'production'
      );
    }
  });
</script>
```

## 📊 Discord Webhook Format

Os erros chegam no Discord com este formato épico:

```
🔴 TypeError - COMPONENT
```
TypeError: Cannot read property 'name' of undefined

📍 **Página:** /dashboard
🎚️ **Severidade:** HIGH  
🌍 **Ambiente:** production

🏷️ **Tags:** type:component, error:typeerror, component:UserProfile, environment:production

👤 **User ID:** user_123
🔗 **Session ID:** session_456

📚 **Stack Trace:**
```
TypeError: Cannot read property 'name' of undefined
    at UserProfile.svelte:25:15
    at Object.get [as name] (UserProfile.svelte:25:15)
```

**My App • Error ID:** a1b2c3d4

## 🎯 Próximos Passos

1. **Configure** o webhook do Discord
2. **Adicione** o setup global no layout
3. **Use** os wrappers nas suas funções
4. **Monitore** os erros em tempo real
5. **Ajuste** a configuração conforme necessário

## 💡 Dicas Pro

- **Use `safeCall`** para todas chamadas de API
- **Configure Discord** para produção
- **Teste** com erros intencionais primeiro
- **Monitore** a performance dos timeouts
- **Ajuste** os thresholds por ambiente

---

**MODO PICA DAS GALAXIAS ATIVADO! 🚀🔥**

Agora você tem um sistema de error tracking COMPLETO e profissional!
