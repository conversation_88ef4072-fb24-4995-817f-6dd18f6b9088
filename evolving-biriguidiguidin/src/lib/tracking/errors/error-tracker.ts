// 🔥 ERROR TRACKING MODULE - MODO PICA DAS GALAXIAS! 🔥
// Sistema de captura e logging de erros com webhook do Discord

import { browser } from '$app/environment';
import type { ErrorConfig, ErrorEvent, LoadingTimeoutOptions } from '../types/tracking.types';
import { getCurrentTracker } from '../index';

/**
 * Configuração padrão do error tracker
 */
const DEFAULT_ERROR_CONFIG: ErrorConfig = {
    discordWebhookUrl: undefined,
    enableDiscordLogging: false,
    enableConsoleLogging: true,
    loadingTimeoutThreshold: 10000, // 10 segundos
    retryAttempts: 3,
    environment: 'development',
    projectName: 'My App'
};

/**
 * 🎯 ERROR TRACKER CLASS - O CARA QUE VAI SALVAR SUA VIDA!
 */
export class ErrorTracker {
    private config: ErrorConfig;
    private errorQueue: ErrorEvent[] = [];
    private isProcessing = false;

    constructor(config: Partial<ErrorConfig> = {}) {
        this.config = { ...DEFAULT_ERROR_CONFIG, ...config };

        if (browser) {
            this.setupGlobalErrorHandlers();
        }

        if (this.config.enableConsoleLogging && import.meta.env.DEV) {
            console.log('🚨 Error Tracker inicializado! MODO PICA DAS GALAXIAS ATIVADO!');
        }
    }

    /**
     * 🔍 RASTREADOR DE ORIGEM - Descobre onde a função foi chamada
     */
    private getCallStackInfo(): {
        callerFunction: string;
        fileName: string;
        lineNumber: string;
        fullStack: string
    } {
        const stack = new Error().stack || '';
        const stackLines = stack.split('\n');

        // Procura pela primeira linha que não seja do error-tracker
        let relevantLine = '';
        let callerFunction = 'Unknown';
        let fileName = 'Unknown';
        let lineNumber = 'Unknown';

        for (let i = 1; i < stackLines.length; i++) {
            const line = stackLines[i];

            // Pula linhas do próprio error-tracker
            if (line.includes('error-tracker.ts') || line.includes('ErrorTracker')) {
                continue;
            }

            relevantLine = line;
            break;
        }

        if (relevantLine) {
            // Extrai informações da linha do stack trace
            // Formato típico: "    at functionName (file:///path/file.ts:line:column)"
            const match = relevantLine.match(/at\s+([^(]+)\s*\(([^:]+):(\d+):\d+\)/);
            if (match) {
                callerFunction = match[1].trim() || 'Anonymous';
                const fullPath = match[2];
                fileName = fullPath.split('/').pop() || fullPath;
                lineNumber = match[3];
            } else {
                // Formato alternativo: "    at file:///path/file.ts:line:column"
                const simpleMatch = relevantLine.match(/at\s+([^:]+):(\d+):\d+/);
                if (simpleMatch) {
                    const fullPath = simpleMatch[1];
                    fileName = fullPath.split('/').pop() || fullPath;
                    lineNumber = simpleMatch[2];
                }
            }
        }

        return {
            callerFunction,
            fileName,
            lineNumber,
            fullStack: stack
        };
    }

    /**
     * 🕸️ CAPTURA ERROS DE TRY/CATCH - Para suas chamadas HTTP e operações assíncronas
     */
    async catchError<T>(
        operation: () => Promise<T> | T,
        context: string,
        additionalData?: Record<string, any>
    ): Promise<T | null> {
        try {
            const result = await operation();
            return result;
        } catch (error) {
            this.logCaughtError(error as Error, context, additionalData);
            return null;
        }
    }

    /**
     * 🧪 LOG ERRO CAPTURADO - Manual logging para try/catch
     */
    logCaughtError(
        error: Error,
        context: string,
        additionalData?: Record<string, any>
    ): void {
        const errorEvent = this.createErrorEvent(
            'caught',
            error,
            {
                context,
                ...additionalData
            },
            this.determineSeverity(error, 'caught')
        );

        this.processError(errorEvent);
    }

    /**
     * 🎨 LOG ERRO DE COMPONENTE - Para erros do Svelte/SvelteKit
     */
    logComponentError(
        error: Error,
        componentName: string,
        props?: Record<string, any>
    ): void {
        const errorEvent = this.createErrorEvent(
            'component',
            error,
            {
                componentName,
                props,
                framework: 'Svelte'
            },
            this.determineSeverity(error, 'component')
        );

        this.processError(errorEvent);
    }

    /**
     * ⏱️ TIMEOUT WRAPPER - Para operações que podem demorar muito
     */
    async withTimeout<T>(
        operation: () => Promise<T>,
        options: LoadingTimeoutOptions
    ): Promise<T | null> {
        const { timeout, errorMessage, context, retryable = false } = options;

        return new Promise(async (resolve, reject) => {
            const timeoutId = setTimeout(() => {
                const error = new Error(errorMessage || `Operação excedeu ${timeout}ms`);

                const errorEvent = this.createErrorEvent(
                    'loading_timeout',
                    error,
                    {
                        timeout,
                        retryable,
                        ...context
                    },
                    'high'
                );

                this.processError(errorEvent);
                resolve(null);
            }, timeout);

            try {
                const result = await operation();
                clearTimeout(timeoutId);
                resolve(result);
            } catch (error) {
                clearTimeout(timeoutId);
                this.logCaughtError(error as Error, 'timeout_operation', context);
                resolve(null);
            }
        });
    }

    /**
     * 🎯 SETUP GLOBAL ERROR HANDLERS - Captura erros inesperados
     */
    private setupGlobalErrorHandlers(): void {
        // Erros JavaScript não capturados
        window.addEventListener('error', (event) => {
            const error = new Error(event.message);
            error.stack = event.error?.stack;

            this.logUnexpectedError(error, {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                type: 'global_error'
            });
        });

        // Promises rejeitadas não capturadas
        window.addEventListener('unhandledrejection', (event) => {
            const error = new Error(
                event.reason?.message ||
                String(event.reason) ||
                'Unhandled Promise Rejection'
            );

            this.logUnexpectedError(error, {
                reason: event.reason,
                type: 'unhandled_rejection'
            });
        });
    }

    /**
     * 💥 LOG ERRO INESPERADO
     */
    private logUnexpectedError(
        error: Error,
        additionalData?: Record<string, any>
    ): void {
        const errorEvent = this.createErrorEvent(
            'unexpected',
            error,
            additionalData,
            'critical'
        );

        this.processError(errorEvent);
    }

    /**
     * 🏭 FACTORY DE EVENTOS DE ERRO
     */
    private createErrorEvent(
        type: ErrorEvent['type'],
        error: Error,
        additionalData?: Record<string, any>,
        severity: ErrorEvent['severity'] = 'medium'
    ): ErrorEvent {
        const tracker = getCurrentTracker();
        const session = tracker?.getSessionInfo();
        const callStack = this.getCallStackInfo();

        return {
            id: crypto.randomUUID(),
            type,
            timestamp: new Date().toISOString(),
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack,
                code: (error as any).code
            },
            context: {
                page: window.location.pathname,
                userAgent: navigator.userAgent,
                url: window.location.href,
                userId: session?.userId,
                sessionId: session?.sessionId,
                // 🔥 INFORMAÇÕES DE ORIGEM - AQUI ESTÁ A MÁGICA!
                callerFunction: callStack.callerFunction,
                fileName: callStack.fileName,
                lineNumber: callStack.lineNumber,
                fullStackTrace: callStack.fullStack,
                additionalData
            },
            severity,
            tags: this.generateTags(type, error, additionalData)
        };
    }

    /**
     * 🏷️ GERADOR DE TAGS INTELIGENTE
     */
    private generateTags(
        type: string,
        error: Error,
        additionalData?: Record<string, any>
    ): string[] {
        const tags = [
            `type:${type}`,
            `environment:${this.config.environment}`,
            `project:${this.config.projectName}`
        ];

        // Tags baseadas no erro
        if (error.name) tags.push(`error:${error.name.toLowerCase()}`);
        if (error.message.includes('fetch')) tags.push('category:network');
        if (error.message.includes('timeout')) tags.push('category:timeout');
        if (error.message.includes('404')) tags.push('status:404');
        if (error.message.includes('500')) tags.push('status:500');

        // Tags do contexto
        if (additionalData?.componentName) {
            tags.push(`component:${additionalData.componentName}`);
        }

        return tags;
    }

    /**
     * 🎚️ DETERMINADOR DE SEVERIDADE INTELIGENTE
     */
    private determineSeverity(
        error: Error,
        type: string
    ): ErrorEvent['severity'] {
        // Erros críticos
        if (type === 'unexpected') return 'critical';
        if (error.message.includes('500') || error.message.includes('502')) return 'critical';
        if (error.name === 'TypeError' && type === 'component') return 'high';

        // Erros altos
        if (type === 'loading_timeout') return 'high';
        if (error.message.includes('401') || error.message.includes('403')) return 'high';
        if (error.name === 'ReferenceError') return 'high';

        // Erros médios
        if (error.message.includes('404')) return 'medium';
        if (type === 'caught' && error.name === 'Error') return 'medium';

        // Erros baixos (padrão)
        return 'low';
    }

    /**
     * ⚡ PROCESSADOR DE ERROS
     */
    private async processError(errorEvent: ErrorEvent): Promise<void> {
        // Log local sempre
        if (this.config.enableConsoleLogging) {
            this.logToConsole(errorEvent);
        }

        // Adiciona à queue para Discord
        this.errorQueue.push(errorEvent);

        // Integra com tracking principal
        const tracker = getCurrentTracker();
        if (tracker) {
            tracker.trackCustomEvent(`error_${errorEvent.type}`, {
                errorId: errorEvent.id,
                severity: errorEvent.severity,
                errorName: errorEvent.error.name,
                tags: errorEvent.tags
            });
        }

        // Processa queue do Discord
        if (this.config.enableDiscordLogging && this.config.discordWebhookUrl) {
            this.processDiscordQueue();
        }
    }

    /**
     * 📝 LOG NO CONSOLE ESTILIZADO
     */
    private logToConsole(errorEvent: ErrorEvent): void {
        if (!import.meta.env.DEV) return; // Só loga em desenvolvimento

        const emoji = this.getSeverityEmoji(errorEvent.severity);
        const color = this.getSeverityColor(errorEvent.severity);

        console.group(`${emoji} ERROR ${errorEvent.type.toUpperCase()} - ${errorEvent.severity.toUpperCase()}`);
        console.log(`%c${errorEvent.error.name}: ${errorEvent.error.message}`, `color: ${color}; font-weight: bold;`);

        console.log('📄 Página:', errorEvent.context.page);
        console.log('🏷️ Tags:', errorEvent.tags);

        if (errorEvent.context.additionalData && Object.keys(errorEvent.context.additionalData).length > 0) {
            console.log('📋 Dados adicionais:', errorEvent.context.additionalData);
        }

        if (errorEvent.error.stack) {
            console.log('📚 Stack completo:', errorEvent.error.stack);
        }
        console.groupEnd();
    }

    /**
     * 🎨 HELPERS DE ESTILO
     */
    private getSeverityEmoji(severity: string): string {
        const emojis = {
            low: '💙',
            medium: '💛',
            high: '🧡',
            critical: '🔴'
        };
        return emojis[severity as keyof typeof emojis] || '⚪';
    }

    private getSeverityColor(severity: string): string {
        const colors = {
            low: '#3b82f6',
            medium: '#eab308',
            high: '#f97316',
            critical: '#ef4444'
        };
        return colors[severity as keyof typeof colors] || '#6b7280';
    }

    /**
     * 🎮 DISCORD WEBHOOK PROCESSOR - O CORAÇÃO DO SISTEMA!
     */
    private async processDiscordQueue(): Promise<void> {
        if (this.isProcessing || this.errorQueue.length === 0) return;

        this.isProcessing = true;

        try {
            const errorsToSend = [...this.errorQueue];
            this.errorQueue = [];

            await this.sendToDiscord(errorsToSend);
        } catch (error) {
            console.error('❌ Falha ao enviar erros para Discord:', error);
            // Recoloca na queue se falhar
            this.errorQueue.unshift(...this.errorQueue);
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 🚀 ENVIO PARA DISCORD
     */
    private async sendToDiscord(errors: ErrorEvent[]): Promise<void> {
        if (!this.config.discordWebhookUrl) return;

        for (const error of errors) {
            const embed = this.createDiscordEmbed(error);

            try {
                await fetch(this.config.discordWebhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: `${this.config.projectName} - Error Bot`,
                        avatar_url: 'https://cdn-icons-png.flaticon.com/512/5602/5602732.png',
                        embeds: [embed]
                    })
                });

                if (this.config.enableConsoleLogging && import.meta.env.DEV) {
                    console.log(`🎯 Erro ${error.id} enviado para Discord!`);
                }
            } catch (discordError) {
                console.error('❌ Falha ao enviar para Discord:', discordError);
            }
        }
    }

    /**
     * 🎨 CRIADOR DE EMBED DO DISCORD
     */
    private createDiscordEmbed(error: ErrorEvent): any {
        const color = this.getDiscordColor(error.severity);
        const emoji = this.getSeverityEmoji(error.severity);

        return {
            title: `${emoji} ${error.error.name} - ${error.type.toUpperCase()}`,
            description: `\`\`\`\n${error.error.message}\n\`\`\``,
            color: color,
            timestamp: error.timestamp,
            fields: [
                {
                    name: '📍 Página',
                    value: error.context.page,
                    inline: true
                },
                {
                    name: '🎚️ Severidade',
                    value: error.severity.toUpperCase(),
                    inline: true
                },
                {
                    name: '🌍 Ambiente',
                    value: this.config.environment,
                    inline: true
                },
                {
                    name: '🏷️ Tags',
                    value: error.tags.join(', ') || 'Nenhuma',
                    inline: false
                },
                ...(error.context.userId ? [{
                    name: '👤 User ID',
                    value: error.context.userId,
                    inline: true
                }] : []),
                ...(error.context.sessionId ? [{
                    name: '🔗 Session ID',
                    value: error.context.sessionId,
                    inline: true
                }] : []),
                ...(error.error.stack ? [{
                    name: '📚 Stack Trace',
                    value: `\`\`\`\n${error.error.stack.slice(0, 1000)}${error.error.stack.length > 1000 ? '...' : ''}\n\`\`\``,
                    inline: false
                }] : [])
            ],
            footer: {
                text: `${this.config.projectName} • Error ID: ${error.id.slice(0, 8)}`,
                icon_url: 'https://cdn-icons-png.flaticon.com/512/2920/2920277.png'
            }
        };
    }

    /**
     * 🌈 CORES DO DISCORD POR SEVERIDADE
     */
    private getDiscordColor(severity: string): number {
        const colors = {
            low: 0x3b82f6,     // Azul
            medium: 0xeab308,  // Amarelo
            high: 0xf97316,    // Laranja
            critical: 0xef4444 // Vermelho
        };
        return colors[severity as keyof typeof colors] || 0x6b7280;
    }

    /**
     * 🔧 MÉTODOS PÚBLICOS PARA CONFIGURAÇÃO
     */

    /**
     * Atualizar configuração do Discord
     */
    setDiscordWebhook(webhookUrl: string): void {
        this.config.discordWebhookUrl = webhookUrl;
        this.config.enableDiscordLogging = true;

        if (this.config.enableConsoleLogging && import.meta.env.DEV) {
            console.log('🎯 Discord webhook configurado! Erros serão enviados automaticamente.');
        }
    }

    /**
     * Definir ambiente
     */
    setEnvironment(env: 'development' | 'staging' | 'production'): void {
        this.config.environment = env;
    }

    /**
     * Forçar flush da queue
     */
    async flush(): Promise<void> {
        if (this.config.enableDiscordLogging && this.config.discordWebhookUrl) {
            await this.processDiscordQueue();
        }
    }

    /**
     * Obter estatísticas de erros
     */
    getStats(): { queueSize: number; config: ErrorConfig } {
        return {
            queueSize: this.errorQueue.length,
            config: this.config
        };
    }
}

// 🌟 SINGLETON INSTANCE
let errorTrackerInstance: ErrorTracker | null = null;

/**
 * 🎯 GET ERROR TRACKER INSTANCE
 */
export function getErrorTracker(config?: Partial<ErrorConfig>): ErrorTracker {
    if (!errorTrackerInstance && browser) {
        errorTrackerInstance = new ErrorTracker(config);
    }
    return errorTrackerInstance!;
}

/**
 * 🚀 HELPER FUNCTIONS - MODO FÁCIL DE USAR!
 */

/**
 * 🕸️ Wrapper para operações que podem falhar
 */
export async function safeCall<T>(
    operation: () => Promise<T> | T,
    context: string,
    additionalData?: Record<string, any>,
    config?: Partial<ErrorConfig>
): Promise<T | null> {
    const tracker = getErrorTracker(config);
    return tracker.catchError(operation, context, additionalData);
}

/**
 * ⏱️ Wrapper para operações com timeout
 */
export async function withTimeout<T>(
    operation: () => Promise<T>,
    timeout: number,
    errorMessage?: string,
    config?: Partial<ErrorConfig>
): Promise<T | null> {
    const tracker = getErrorTracker(config);
    return tracker.withTimeout(operation, {
        timeout,
        errorMessage,
        retryable: false
    });
}

/**
 * 🧪 Log manual de erro capturado
 */
export function logError(
    error: Error,
    context: string,
    additionalData?: Record<string, any>,
    config?: Partial<ErrorConfig>
): void {
    const tracker = getErrorTracker(config);
    tracker.logCaughtError(error, context, additionalData);
}

/**
 * 🎨 Log erro de componente Svelte
 */
export function logComponentError(
    error: Error,
    componentName: string,
    props?: Record<string, any>,
    config?: Partial<ErrorConfig>
): void {
    const tracker = getErrorTracker(config);
    tracker.logComponentError(error, componentName, props);
}

/**
 * 🔧 Configurar Discord webhook rapidamente
 */
export function setupDiscordErrorLogging(
    webhookUrl: string,
    projectName: string,
    environment: 'development' | 'staging' | 'production' = 'development'
): ErrorTracker {
    const tracker = getErrorTracker({
        discordWebhookUrl: webhookUrl,
        enableDiscordLogging: true,
        projectName,
        environment
    });

    if (import.meta.env.DEV) {
        console.log('🚨 MODO PICA DAS GALAXIAS ATIVADO! Discord error logging configurado! 🚀');
    }
    return tracker;
}
