// 🔥 SVELTE ERROR HANDLERS - INTEGRAÇÃO PERFEITA COM SVELTEKIT! 🔥

import { browser } from '$app/environment';
import { getErrorTracker, logComponentError } from './error-tracker';
import type { ErrorConfig } from '../types/tracking.types';

/**
 * 🎨 SVELTE ERROR BOUNDARY ACTION
 * Use como action em qualquer elemento para capturar erros
 */
export function errorBoundary(
    node: HTMLElement,
    options: {
        componentName?: string;
        onError?: (error: Error) => void;
        config?: Partial<ErrorConfig>;
    } = {}
) {
    const { componentName = 'Unknown Component', onError, config } = options;

    function handleError(event: ErrorEvent) {
        const error = new Error(event.message || 'Component Error');
        error.stack = event.error?.stack;

        logComponentError(error, componentName, {
            element: node.tagName,
            id: node.id,
            className: node.className
        }, config);

        onError?.(error);
    }

    // Escuta erros no elemento e seus filhos
    node.addEventListener('error', handleError, true);

    return {
        update(newOptions: typeof options) {
            Object.assign(options, newOptions);
        },
        destroy() {
            node.removeEventListener('error', handleError, true);
        }
    };
}

/**
 * 🚦 ASYNC LOAD COMPONENT - Com timeout automático
 */
export async function loadComponent<T = any>(
    loader: () => Promise<T>,
    componentName: string,
    timeout: number = 5000,
    config?: Partial<ErrorConfig>
): Promise<T | null> {
    const tracker = getErrorTracker(config);

    return tracker.withTimeout(
        async () => {
            try {
                return await loader();
            } catch (error) {
                tracker.logComponentError(
                    error as Error,
                    componentName,
                    {
                        type: 'dynamic_import',
                        timeout
                    }
                );
                throw error;
            }
        },
        {
            timeout,
            errorMessage: `Componente ${componentName} demorou mais que ${timeout}ms para carregar`,
            context: { componentName, type: 'component_loading' }
        }
    );
}

/**
 * 🎯 HTTP CLIENT COM ERROR TRACKING
 */
export class TrackedFetch {
    private config?: Partial<ErrorConfig>;

    constructor(config?: Partial<ErrorConfig>) {
        this.config = config;
    }

    async get<T = any>(url: string, options?: RequestInit): Promise<T | null> {
        return this.request<T>(url, { ...options, method: 'GET' });
    }

    async post<T = any>(url: string, data?: any, options?: RequestInit): Promise<T | null> {
        return this.request<T>(url, {
            ...options,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }

    async put<T = any>(url: string, data?: any, options?: RequestInit): Promise<T | null> {
        return this.request<T>(url, {
            ...options,
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }

    async delete<T = any>(url: string, options?: RequestInit): Promise<T | null> {
        return this.request<T>(url, { ...options, method: 'DELETE' });
    }

    private async request<T>(url: string, options?: RequestInit): Promise<T | null> {
        const tracker = getErrorTracker(this.config);

        return tracker.catchError(async () => {
            const response = await fetch(url, options);

            if (!response.ok) {
                const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
                (error as any).status = response.status;
                (error as any).url = url;
                (error as any).method = options?.method || 'GET';
                throw error;
            }

            const contentType = response.headers.get('content-type');
            if (contentType?.includes('application/json')) {
                return await response.json();
            }

            return await response.text() as any;
        }, 'http_request', {
            url,
            method: options?.method || 'GET',
            type: 'api_call'
        });
    }
}

/**
 * 🎮 PROMISE WRAPPER COM RETRY
 */
export async function retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    context: string = 'retry_operation',
    config?: Partial<ErrorConfig>
): Promise<T | null> {
    const tracker = getErrorTracker(config);
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await operation();
        } catch (error) {
            lastError = error as Error;

            if (attempt === maxRetries) {
                tracker.logCaughtError(lastError, context, {
                    attempts: maxRetries,
                    finalAttempt: true,
                    type: 'retry_exhausted'
                });
                break;
            }

            // Log tentativa falha (só em debug)
            if (tracker.getStats().config.enableConsoleLogging) {
                console.warn(`🔄 Tentativa ${attempt}/${maxRetries} falhou:`, error);
            }

            // Espera antes da próxima tentativa
            await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
    }

    return null;
}

/**
 * 🎨 FORM VALIDATION ERROR TRACKER
 */
export function createFormErrorTracker(formName: string, config?: Partial<ErrorConfig>) {
    const tracker = getErrorTracker(config);

    return {
        /**
         * Log erro de validação
         */
        logValidationError(field: string, error: string, value?: any) {
            const validationError = new Error(`Validation failed for field: ${field}`);

            tracker.logCaughtError(validationError, 'form_validation', {
                formName,
                field,
                validationError: error,
                value: typeof value === 'string' ? value.slice(0, 100) : value,
                type: 'validation_error'
            });
        },

        /**
         * Log erro de submit
         */
        logSubmitError(error: Error, formData?: Record<string, any>) {
            tracker.logCaughtError(error, 'form_submit', {
                formName,
                formData: formData ? Object.keys(formData) : undefined,
                type: 'submit_error'
            });
        },

        /**
         * Wrapper para submit com tracking
         */
        async safeSubmit<T>(
            submitFn: () => Promise<T>,
            formData?: Record<string, any>
        ): Promise<T | null> {
            return tracker.catchError(submitFn, 'form_submit', {
                formName,
                formData: formData ? Object.keys(formData) : undefined,
                type: 'form_submission'
            });
        }
    };
}

/**
 * 🚀 STORE ERROR TRACKER - Para Svelte stores
 */
export function createStoreErrorTracker(storeName: string, config?: Partial<ErrorConfig>) {
    const tracker = getErrorTracker(config);

    return {
        /**
         * Wrapper para operações de store
         */
        async safeUpdate<T>(
            updateFn: () => Promise<T> | T,
            context: string = 'store_update'
        ): Promise<T | null> {
            return tracker.catchError(updateFn, context, {
                storeName,
                type: 'store_operation'
            });
        },

        /**
         * Log erro de store
         */
        logStoreError(error: Error, operation: string, data?: any) {
            tracker.logCaughtError(error, 'store_error', {
                storeName,
                operation,
                data: typeof data === 'object' ? Object.keys(data) : data,
                type: 'store_error'
            });
        }
    };
}

/**
 * 🎯 NAVIGATION ERROR TRACKER - Para erros de roteamento
 */
export function createNavigationErrorTracker(config?: Partial<ErrorConfig>) {
    const tracker = getErrorTracker(config);

    return {
        /**
         * Wrapper seguro para navegação
         */
        async safeGoto(url: string, replaceState?: boolean) {
            return tracker.catchError(async () => {
                const { goto } = await import('$app/navigation');
                return goto(url, { replaceState });
            }, 'navigation', {
                url,
                replaceState,
                type: 'programmatic_navigation'
            });
        },

        /**
         * Log erro de navegação
         */
        logNavigationError(error: Error, fromPath: string, toPath: string) {
            tracker.logCaughtError(error, 'navigation_error', {
                fromPath,
                toPath,
                type: 'navigation_error'
            });
        }
    };
}

/**
 * 🎮 GAME-CHANGER: GLOBAL ERROR SETUP
 * Call this once in your main layout to set up everything
 */
export function setupGlobalErrorTracking(
    discordWebhookUrl?: string,
    projectName: string = 'My SvelteKit App',
    environment: 'development' | 'staging' | 'production' = 'development'
) {
    if (!browser) return null;

    const config: Partial<ErrorConfig> = {
        projectName,
        environment,
        enableConsoleLogging: environment === 'development',
        enableDiscordLogging: !!discordWebhookUrl,
        discordWebhookUrl,
        loadingTimeoutThreshold: environment === 'production' ? 15000 : 10000
    };

    const tracker = getErrorTracker(config);

    // Se tem webhook, configura Discord
    if (discordWebhookUrl) {
        tracker.setDiscordWebhook(discordWebhookUrl);
    }

    console.log('🚨 MODO PICA DAS GALAXIAS ATIVADO! Global error tracking configurado! 🚀');

    return tracker;
}

/**
 * 🎯 INSTANCE HELPERS
 */
export const trackedFetch = new TrackedFetch();

// Export para usar em qualquer lugar
export { getErrorTracker, logError, logComponentError, withTimeout, safeCall } from './error-tracker';
