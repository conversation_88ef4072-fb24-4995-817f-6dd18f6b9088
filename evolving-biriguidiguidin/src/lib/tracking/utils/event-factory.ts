// Event factory utilities for creating standardized tracking events

import type { TrackingEvent, EventType, EventMetadata } from '../types/tracking.types';

/**
 * Create a base tracking event with common properties
 */
export function createBaseEvent(
    type: EventType,
    metadata: Partial<EventMetadata> = {}
): Omit<TrackingEvent, 'project_id' | 'session_id'> {
    return {
        event_type: type,
        page_path: getCurrentPath(),
        metadata: {
            ...metadata,
            user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
            screen_size: getScreenSize(),
            viewport_size: getViewportSize(),
            referrer: typeof document !== 'undefined' ? (document.referrer || undefined) : undefined,
        },
        timestamp: new Date().toISOString(),
    };
}

/**
 * Create a page view event
 */
export function createPageViewEvent(
    path: string,
    metadata: Partial<EventMetadata> = {}
): Omit<TrackingEvent, 'project_id' | 'session_id'> {
    return createBaseEvent('page_enter', {
        ...metadata,
        referrer: typeof document !== 'undefined' ? (document.referrer || undefined) : undefined,
    });
}

/**
 * Create a page exit event
 */
export function createPageExitEvent(
    path: string,
    duration: number,
    metadata: Partial<EventMetadata> = {}
): Omit<TrackingEvent, 'project_id' | 'session_id'> {
    return {
        ...createBaseEvent('page_exit', metadata),
        duration,
    };
}

/**
 * Create a button click event
 */
export function createButtonClickEvent(
    element: HTMLElement,
    metadata: Partial<EventMetadata> = {}
): Omit<TrackingEvent, 'project_id' | 'session_id'> {
    const rect = element.getBoundingClientRect();

    return createBaseEvent('button_click', {
        ...metadata,
        element_id: element.id || undefined,
        element_class: element.className || undefined,
        element_text: getElementText(element),
        click_position: {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2
        }
    });
}

/**
 * Create a custom event
 */
export function createCustomEvent(
    customType: string,
    metadata: Partial<EventMetadata> = {}
): Omit<TrackingEvent, 'project_id' | 'session_id'> {
    return createBaseEvent('custom_event', {
        ...metadata,
        custom_event_type: customType
    });
}

// Utility functions

/**
 * Get current page path
 */
function getCurrentPath(): string {
    if (typeof window === 'undefined') return '/';
    return window.location.pathname;
}

/**
 * Get screen size as string
 */
function getScreenSize(): string | undefined {
    if (typeof screen === 'undefined') return undefined;
    return `${screen.width}x${screen.height}`;
}

/**
 * Get viewport size as string
 */
function getViewportSize(): string | undefined {
    if (typeof window === 'undefined') return undefined;
    return `${window.innerWidth}x${window.innerHeight}`;
}

/**
 * Get meaningful text from element
 */
function getElementText(element: HTMLElement): string | undefined {
    const text = element.textContent || element.innerText || element.getAttribute('aria-label') || element.getAttribute('title');
    return text ? text.trim().slice(0, 100) : undefined;
}

/**
 * Utility class for advanced event creation and manipulation
 */
export class TrackingEventFactory {
    private projectId: string;
    private sessionId: string;
    private userId?: string;

    constructor(projectId: string, sessionId: string, userId?: string) {
        this.projectId = projectId;
        this.sessionId = sessionId;
        this.userId = userId;
    }

    /**
     * Create a complete tracking event with project and session info
     */
    createEvent(
        type: EventType,
        metadata: Partial<EventMetadata> = {},
        duration?: number
    ): TrackingEvent {
        const baseEvent = createBaseEvent(type, metadata);

        return {
            ...baseEvent,
            project_id: this.projectId,
            session_id: this.sessionId,
            user_id: this.userId,
            duration
        };
    }

    /**
     * Update session context
     */
    updateSession(sessionId: string, userId?: string): void {
        this.sessionId = sessionId;
        this.userId = userId;
    }
}

/**
 * Tracking utilities for common operations
 */
export class TrackingUtils {
    /**
     * Debounce function for high-frequency events
     */
    static debounce<T extends (...args: any[]) => any>(
        func: T,
        wait: number
    ): (...args: Parameters<T>) => void {
        let timeout: ReturnType<typeof setTimeout>;
        return (...args: Parameters<T>) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func(...args), wait);
        };
    }

    /**
     * Throttle function for scroll/resize events
     */
    static throttle<T extends (...args: any[]) => any>(
        func: T,
        limit: number
    ): (...args: Parameters<T>) => void {
        let lastFunc: ReturnType<typeof setTimeout>;
        let lastRan: number;
        return (...args: Parameters<T>) => {
            if (!lastRan) {
                func(...args);
                lastRan = Date.now();
            } else {
                clearTimeout(lastFunc);
                lastFunc = setTimeout(() => {
                    if ((Date.now() - lastRan) >= limit) {
                        func(...args);
                        lastRan = Date.now();
                    }
                }, limit - (Date.now() - lastRan));
            }
        };
    }

    /**
     * Get element selector for tracking
     */
    static getElementSelector(element: HTMLElement): string {
        if (element.id) {
            return `#${element.id}`;
        }

        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                return `.${classes.join('.')}`;
            }
        }

        return element.tagName.toLowerCase();
    }

    /**
     * Check if element is visible in viewport
     */
    static isElementVisible(element: HTMLElement): boolean {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Get scroll percentage of page
     */
    static getScrollPercentage(): number {
        const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        return scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
    }

    /**
     * Sanitize data for tracking (remove PII)
     */
    static sanitizeData(data: Record<string, any>): Record<string, any> {
        const sensitiveFields = [
            'password', 'email', 'phone', 'ssn', 'credit_card',
            'address', 'name', 'firstName', 'lastName'
        ];

        const sanitized: Record<string, any> = {};

        for (const [key, value] of Object.entries(data)) {
            const lowerKey = key.toLowerCase();

            if (sensitiveFields.some(field => lowerKey.includes(field))) {
                sanitized[key] = '[REDACTED]';
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = this.sanitizeData(value);
            } else {
                sanitized[key] = value;
            }
        }

        return sanitized;
    }

    /**
     * Check if user has Do Not Track enabled
     */
    static respectsDoNotTrack(): boolean {
        if (typeof navigator === 'undefined') return false;
        return navigator.doNotTrack === '1' ||
            (window as any).doNotTrack === '1' ||
            (navigator as any).msDoNotTrack === '1';
    }

    /**
     * Check if local storage is available
     */
    static isLocalStorageAvailable(): boolean {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch {
            return false;
        }
    }
}