import PocketBase from 'pocketbase';
import { browser } from '$app/environment';

export const pb = new PocketBase(
  import.meta.env.VITE_POCKETBASE_URL || 'http://localhost:8090'
);

// Auto refresh auth token
if (browser) {
  pb.authStore.onChange(() => {
    console.log('Auth state changed:', pb.authStore.isValid);
  });
}

// Helper para verificar se está autenticado
export function isAuthenticated() {
  return pb.authStore.isValid;
}

// Helper para pegar usuário atual
export function getCurrentUser() {
  return pb.authStore.model;
}