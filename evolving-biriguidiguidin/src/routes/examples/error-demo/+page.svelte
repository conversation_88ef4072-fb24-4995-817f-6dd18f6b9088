<!-- 🚨 EXEMPLO DE USO DO ERROR TRACKING - MODO PICA DAS GALAXIAS! 🚀 -->
<script lang="ts">
	import { onMount } from 'svelte';
	import {
		setupGlobalErrorTracking,
		logError,
		safeCall,
		errorBoundary,
		withTimeout,
		trackedFetch,
		retryOperation,
		createFormErrorTracker
	} from '$lib/tracking/errors/svelte-helpers';

	// Setup do error tracking
	let errorTracker: any = null;
	let showResults = false;
	let results: string[] = [];

	const formTracker = createFormErrorTracker('ErrorTestForm');

	onMount(() => {
		// Setup básico para development (sem Discord)
		errorTracker = setupGlobalErrorTracking(
			'https://discord.com/api/webhooks/1380932394042785873/L96aaZSOYt9DJwAlVndjoMa2UXybUev9HLssC4BhxoZmjmACHPxGta-BqrUr-Ha1-1T5', // Sem Discord por agora
			'Error Tracking Demo',
			'development'
		);

		// Para testar com Discord, descomente e adicione sua webhook URL:
		// errorTracker = setupGlobalErrorTracking(
		//   'https://discord.com/api/webhooks/YOUR_WEBHOOK_URL',
		//   'Error Tracking Demo',
		//   'development'
		// );

		results.push('✅ Error tracking inicializado!');
		showResults = true;
	});

	// 🕸️ TESTE 1: Try/Catch Error com safeCall
	async function testSafeCall() {
		results.push('🧪 Testando safeCall...');

		const result = await safeCall(
			() => {
				throw new Error('Erro simulado de API');
			},
			'test_safe_call',
			{ testId: 1, operation: 'simulation' }
		);

		if (result === null) {
			results.push('✅ Erro capturado com safeCall (check console)');
		}
		results = [...results];
	}

	// ⏱️ TESTE 2: Timeout Error
	async function testTimeout() {
		results.push('🧪 Testando timeout...');

		const result = await withTimeout(
			() => new Promise((resolve) => setTimeout(resolve, 15000)), // 15 segundos
			3000, // timeout de 3 segundos
			'Operação demorou muito tempo!'
		);

		if (result === null) {
			results.push('✅ Timeout capturado (check console)');
		}
		results = [...results];
	}

	// 🎨 TESTE 3: Component Error (simulado)
	function testComponentError() {
		results.push('🧪 Testando component error...');

		try {
			// Simula erro de componente
			const obj: any = null;
			obj.property.subProperty = 'test'; // Vai dar erro
		} catch (error) {
			logError(error as Error, 'component_test', {
				componentName: 'ErrorTestComponent',
				props: { test: true }
			});
			results.push('✅ Component error logado (check console)');
		}
		results = [...results];
	}

	// 🌐 TESTE 4: HTTP Error com trackedFetch
	async function testHttpError() {
		results.push('🧪 Testando HTTP error...');

		const result = await trackedFetch.get('/api/endpoint-que-nao-existe');

		if (result === null) {
			results.push('✅ HTTP error capturado (check console)');
		}
		results = [...results];
	}

	// 🔄 TESTE 5: Retry Operation
	async function testRetryOperation() {
		results.push('🧪 Testando retry operation...');

		let attempts = 0;
		const result = await retryOperation(
			async () => {
				attempts++;
				if (attempts < 3) {
					throw new Error(`Tentativa ${attempts} falhou`);
				}
				return `Sucesso na tentativa ${attempts}!`;
			},
			3, // 3 tentativas
			500, // 500ms delay
			'test_retry'
		);

		if (result) {
			results.push(`✅ Retry bem-sucedido: ${result}`);
		} else {
			results.push('✅ Retry esgotado (erros logados)');
		}
		results = [...results];
	}

	// 📝 TESTE 6: Form Error
	async function testFormError() {
		results.push('🧪 Testando form error...');

		const result = await formTracker.safeSubmit(
			() => {
				throw new Error('Falha na validação do servidor');
			},
			{ name: 'Test User', email: '<EMAIL>' }
		);

		if (result === null) {
			results.push('✅ Form error capturado (check console)');
		}
		results = [...results];
	}

	// 💥 TESTE 7: Global Error (erro não capturado)
	function testGlobalError() {
		results.push('🧪 Testando global error...');

		setTimeout(() => {
			// Erro que será capturado pelo global handler
			throw new Error('Erro global não capturado!');
		}, 100);

		setTimeout(() => {
			results.push('✅ Global error disparado (check console)');
			results = [...results];
		}, 500);
	}

	// 🎯 TESTE 8: Promise Rejection não capturada
	function testUnhandledRejection() {
		results.push('🧪 Testando unhandled rejection...');

		// Promise rejeitada não capturada
		new Promise((_, reject) => {
			setTimeout(() => reject(new Error('Promise rejection não capturada!')), 100);
		});

		setTimeout(() => {
			results.push('✅ Unhandled rejection disparada (check console)');
			results = [...results];
		}, 500);
	}

	function clearResults() {
		results = [];
		showResults = false;
	}

	function showStats() {
		if (errorTracker) {
			const stats = errorTracker.getStats();
			alert(
				`Error Queue: ${stats.queueSize} errors\nConfig: ${JSON.stringify(stats.config, null, 2)}`
			);
		}
	}
</script>

<div class="mx-auto max-w-4xl p-8">
	<h1 class="mb-6 text-center text-3xl font-bold">
		🚨 Error Tracking Demo - MODO PICA DAS GALAXIAS! 🚀
	</h1>

	<div class="mb-8 rounded-lg border border-blue-200 bg-blue-50 p-4">
		<h2 class="mb-2 text-lg font-semibold">📋 Como Testar:</h2>
		<ol class="list-inside list-decimal space-y-1 text-sm">
			<li>Abra o DevTools (F12) para ver os logs no console</li>
			<li>Clique nos botões abaixo para testar diferentes tipos de erros</li>
			<li>Verifique os logs formatados no console</li>
			<li>Para testar Discord, adicione sua webhook URL no código</li>
		</ol>
	</div>

	<!-- Error Boundary Test -->
	<div
		use:errorBoundary={{
			componentName: 'ErrorTestContainer',
			onError: (error) => {
				results.push(`🎯 Error boundary capturou: ${error.message}`);
				results = [...results];
			}
		}}
		class="mb-8"
	>
		<div class="mb-8 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
			<!-- Try/Catch Tests -->
			<div class="rounded-lg border border-red-200 bg-red-50 p-4">
				<h3 class="mb-3 font-semibold text-red-800">🕸️ Try/Catch Errors</h3>
				<div class="space-y-2">
					<button
						on:click={testSafeCall}
						class="w-full rounded border border-red-300 bg-red-100 px-3 py-2 text-sm hover:bg-red-200"
					>
						Test safeCall()
					</button>
					<button
						on:click={testHttpError}
						class="w-full rounded border border-red-300 bg-red-100 px-3 py-2 text-sm hover:bg-red-200"
					>
						Test HTTP Error
					</button>
					<button
						on:click={testRetryOperation}
						class="w-full rounded border border-red-300 bg-red-100 px-3 py-2 text-sm hover:bg-red-200"
					>
						Test Retry Operation
					</button>
				</div>
			</div>

			<!-- Component Tests -->
			<div class="rounded-lg border border-orange-200 bg-orange-50 p-4">
				<h3 class="mb-3 font-semibold text-orange-800">🎨 Component Errors</h3>
				<div class="space-y-2">
					<button
						on:click={testComponentError}
						class="w-full rounded border border-orange-300 bg-orange-100 px-3 py-2 text-sm hover:bg-orange-200"
					>
						Test Component Error
					</button>
					<button
						on:click={testFormError}
						class="w-full rounded border border-orange-300 bg-orange-100 px-3 py-2 text-sm hover:bg-orange-200"
					>
						Test Form Error
					</button>
					<button
						on:click={testGlobalError}
						class="w-full rounded border border-orange-300 bg-orange-100 px-3 py-2 text-sm hover:bg-orange-200"
					>
						Test Global Error
					</button>
				</div>
			</div>

			<!-- Timeout Tests -->
			<div class="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
				<h3 class="mb-3 font-semibold text-yellow-800">⏱️ Timeout Errors</h3>
				<div class="space-y-2">
					<button
						on:click={testTimeout}
						class="w-full rounded border border-yellow-300 bg-yellow-100 px-3 py-2 text-sm hover:bg-yellow-200"
					>
						Test Timeout
					</button>
					<button
						on:click={testUnhandledRejection}
						class="w-full rounded border border-yellow-300 bg-yellow-100 px-3 py-2 text-sm hover:bg-yellow-200"
					>
						Test Unhandled Rejection
					</button>
					<button
						on:click={showStats}
						class="w-full rounded border border-yellow-300 bg-yellow-100 px-3 py-2 text-sm hover:bg-yellow-200"
					>
						Show Error Stats
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Controls -->
	<div class="mb-6 flex justify-center gap-4">
		<button
			on:click={clearResults}
			class="rounded border border-gray-300 bg-gray-100 px-4 py-2 hover:bg-gray-200"
		>
			Limpar Resultados
		</button>
	</div>

	<!-- Results -->
	{#if showResults && results.length > 0}
		<div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
			<h3 class="mb-3 font-semibold">📊 Resultados dos Testes:</h3>
			<div class="space-y-1 font-mono text-sm">
				{#each results as result}
					<div class="rounded border bg-white p-2">{result}</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Instructions -->
	<div class="mt-8 rounded-lg border border-green-200 bg-green-50 p-4">
		<h3 class="mb-2 font-semibold text-green-800">🎯 Discord Setup (Opcional):</h3>
		<ol class="list-inside list-decimal space-y-1 text-sm text-green-700">
			<li>Crie um webhook no seu servidor Discord</li>
			<li>Copie a URL do webhook</li>
			<li>Descomente o código com Discord no script acima</li>
			<li>Cole sua webhook URL</li>
			<li>Teste novamente - erros aparecerão no Discord!</li>
		</ol>
	</div>
</div>

<style>
	button {
		transition: all 0.2s ease;
	}

	button:hover {
		transform: translateY(-1px);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}
</style>
