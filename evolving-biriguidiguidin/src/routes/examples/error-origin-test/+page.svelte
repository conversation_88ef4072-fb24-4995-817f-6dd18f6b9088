<script lang="ts">
	import { safeCall, logError, getErrorTracker } from '$lib/tracking';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';

	// 🔥 FUNÇÕES DE TESTE PARA DEMONSTRAR O RASTREAMENTO DE ORIGEM

	async function fetchUserData() {
		// Esta função vai falhar e mostrar exatamente onde foi chamada
		const result = await safeCall(
			async () => {
				throw new Error('Falha ao buscar dados do usuário');
			},
			'fetch_user_data',
			{ userId: 123, source: 'user_profile' }
		);

		return result;
	}

	async function processPayment(amount: number) {
		// Outra função que vai falhar e mostrar a origem
		try {
			if (amount <= 0) {
				throw new Error('Valor inválido para pagamento');
			}
			// Simula operação que falha
			throw new Error('Gateway de pagamento indisponível');
		} catch (error) {
			logError(error as Error, 'payment_processing', { amount, paymentMethod: 'credit_card' });
		}
	}

	function calculateTotal(items: any[]) {
		// Função que vai gerar erro de componente
		try {
			// Força um erro de referência
			const total = items.map((item) => item.nonExistentProperty.price).reduce((a, b) => a + b, 0);
			return total;
		} catch (error) {
			// Log manual com origem
			const tracker = getErrorTracker();
			tracker.logComponentError(error as Error, 'CalculatorComponent', { itemCount: items.length });
			return 0;
		}
	}

	// Testa os diferentes tipos de erro
	async function testOriginTracking() {
		console.log('🧪 Testando rastreamento de origem de funções...');

		// Teste 1: safeCall
		await fetchUserData();

		// Teste 2: logError manual
		await processPayment(-100);

		// Teste 3: erro de componente
		calculateTotal([{ name: 'Item 1' }, { name: 'Item 2' }]);
	}
</script>

<div class="container mx-auto space-y-6 p-6">
	<Card>
		<CardHeader>
			<CardTitle>🔍 Teste de Rastreamento de Origem</CardTitle>
			<CardDescription>
				Demonstração do sistema que mostra exatamente onde cada erro foi chamado
			</CardDescription>
		</CardHeader>
		<CardContent class="space-y-4">
			<div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
				<h3 class="mb-2 font-semibold text-blue-800">📍 Como funciona:</h3>
				<ul class="space-y-1 text-sm text-blue-700">
					<li>• <strong>Função:</strong> Nome da função que chamou o error tracker</li>
					<li>• <strong>Arquivo:</strong> Nome do arquivo onde a função está</li>
					<li>• <strong>Linha:</strong> Número da linha no arquivo (quando possível)</li>
					<li>• <strong>Stack completo:</strong> Stack trace completo para análise detalhada</li>
				</ul>
			</div>

			<div class="grid gap-4">
				<Button onclick={testOriginTracking} class="w-full">
					🧪 Testar Rastreamento de Origem
				</Button>
			</div>

			<div class="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
				<h3 class="mb-2 font-semibold text-yellow-800">👁️ Onde ver os resultados:</h3>
				<ul class="space-y-1 text-sm text-yellow-700">
					<li>• <strong>Console do navegador:</strong> Logs detalhados com origem destacada</li>
					<li>• <strong>Discord:</strong> Se configurado, com informações de origem no embed</li>
					<li>• <strong>Tracking principal:</strong> Eventos são registrados no sistema</li>
				</ul>
			</div>

			<div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
				<h3 class="mb-2 font-semibold text-gray-800">🔥 Exemplos que serão testados:</h3>
				<div class="space-y-2 text-sm text-gray-700">
					<div>
						<strong>fetchUserData():</strong> Erro via safeCall
						<br /><span class="text-xs text-gray-500"
							>→ Mostrará origem: fetchUserData em +page.svelte:linha</span
						>
					</div>
					<div>
						<strong>processPayment():</strong> Erro via logError manual
						<br /><span class="text-xs text-gray-500"
							>→ Mostrará origem: processPayment em +page.svelte:linha</span
						>
					</div>
					<div>
						<strong>calculateTotal():</strong> Erro de componente
						<br /><span class="text-xs text-gray-500"
							>→ Mostrará origem: calculateTotal em +page.svelte:linha</span
						>
					</div>
				</div>
			</div>
		</CardContent>
	</Card>
</div>
